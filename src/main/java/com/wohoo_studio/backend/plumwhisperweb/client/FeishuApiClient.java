package com.wohoo_studio.backend.plumwhisperweb.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wohoo_studio.backend.plumwhisperweb.client.base.BaseApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Feishu API 客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeishuApiClient extends BaseApiClient {

    protected FeishuApiClient(ObjectMapper objectMapper) {
        super(objectMapper);
    }

}
