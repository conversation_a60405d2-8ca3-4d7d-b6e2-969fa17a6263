package com.wohoo_studio.backend.plumwhisperweb.client.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 基础API客户端
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseApiClient {

    protected final OkHttpClient httpClient;
    protected final ObjectMapper objectMapper;

    protected BaseApiClient(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 执行HTTP请求
     *
     * @param request HTTP请求
     * @return 响应体字符串
     * @throws IOException 请求异常
     */
    protected String executeRequest(Request request) throws IOException {
        long startTime = System.currentTimeMillis();

        try (Response response = httpClient.newCall(request).execute()) {
            long duration = System.currentTimeMillis() - startTime;

            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                log.error("HTTP请求失败: {} {}, 耗时: {}ms, 错误: {}",
                        request.method(), request.url(), duration, errorBody);
                throw new IOException("HTTP请求失败: " + response.code() + " " + response.message());
            }

            String responseBody = response.body() != null ? response.body().string() : "";
            log.debug("HTTP请求成功: {} {}, 耗时: {}ms",
                    request.method(), request.url(), duration);

            return responseBody;
        }
    }

    /**
     * 创建JSON请求体
     *
     * @param object 要序列化的对象
     * @return RequestBody
     */
    protected RequestBody createJsonRequestBody(Object object) {
        try {
            String json = objectMapper.writeValueAsString(object);
            return RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        } catch (Exception e) {
            throw new RuntimeException("创建JSON请求体失败", e);
        }
    }

    /**
     * 解析JSON响应
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    protected <T> T parseJsonResponse(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("解析JSON响应失败: {}", json, e);
            throw new RuntimeException("解析JSON响应失败", e);
        }
    }
}
