package com.wohoo_studio.backend.plumwhisperweb.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wohoo_studio.backend.plumwhisperweb.client.base.BaseApiClient;
import com.wohoo_studio.backend.plumwhisperweb.config.BaziAnalyzeConfig;
import com.wohoo_studio.backend.plumwhisperweb.dto.ai.model.GeminiRequestDTO;
import com.wohoo_studio.backend.plumwhisperweb.dto.ai.model.GeminiResponseDTO;
import com.wohoo_studio.backend.plumwhisperweb.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Random;

/**
 * Gemini API 客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GeminiApiClient extends BaseApiClient {

    @Autowired
    private BaziAnalyzeConfig baziAnalyzeConfig;

    protected GeminiApiClient(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    /**
     * 使用配置中的 API Key 进行聊天
     *
     * @param prompt 提示文本
     * @return 响应文本
     */
    public String chat(String prompt) {
        List<String> apiKeys = baziAnalyzeConfig.getGemini().getApiKey();
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new BusinessException("未配置 Gemini API Key");
        }

        // 随机选择一个 API Key
        String selectedApiKey = selectApiKey(apiKeys);
        return chat(prompt, selectedApiKey);
    }

    /**
     * 使用指定的 API Key 进行聊天
     *
     * @param prompt 提示文本
     * @param apiKey API Key
     * @return 响应文本
     */
    public String chat(String prompt, String apiKey) {
        try {

            String model = baziAnalyzeConfig.getGemini().getModel();
            String url = String.format("%s/v1beta/models/%s:generateContent?key=%s",
                    baziAnalyzeConfig.getGemini().getBaseUrl(),
                    model,
                    apiKey);

            Request request = new Request.Builder()
                    .url(url)
                    .post(createJsonRequestBody(GeminiRequestDTO.createTextRequest(prompt)))
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 执行请求
            String responseJson = executeRequest(request);
            log.debug("Gemini API 响应: {}", responseJson);

            // 解析响应
            GeminiResponseDTO responseDTO = parseJsonResponse(responseJson, GeminiResponseDTO.class);

            // 提取响应文本
            String responseText = responseDTO.getFirstCandidateText();
            if (responseText == null || responseText.trim().isEmpty()) {
                throw new BusinessException("Gemini API 返回空响应");
            }

            return responseText;

        } catch (IOException e) {
            log.error("调用 Gemini API 失败", e);
            throw new BusinessException("调用 Gemini API 失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理 Gemini API 响应失败", e);
            throw new BusinessException("处理 Gemini API 响应失败: " + e.getMessage());
        }
    }

    /**
     * 从 API Key 列表中选择一个可用的 Key
     * 当前实现是随机选择，可以根据需要改为轮询或其他策略
     *
     * @param apiKeys API Key 列表
     * @return 选中的 API Key
     */
    private String selectApiKey(List<String> apiKeys) {
        if (apiKeys.size() == 1) {
            return apiKeys.get(0);
        }

        // 随机选择策略
        Random random = new Random();
        int index = random.nextInt(apiKeys.size());
        String selectedKey = apiKeys.get(index);

        log.debug("从 {} 个 API Key 中选择了第 {} 个", apiKeys.size(), index + 1);
        return selectedKey;
    }

    /**
     * 获取配置中的第一个 API Key
     *
     * @return 第一个 API Key
     */
    public String getFirstApiKey() {
        List<String> apiKeys = baziAnalyzeConfig.getGemini().getApiKey();
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new BusinessException("未配置 Gemini API Key");
        }
        return apiKeys.get(0);
    }

    /**
     * 获取所有配置的 API Key
     *
     * @return API Key 列表
     */
    public List<String> getAllApiKeys() {
        List<String> apiKeys = baziAnalyzeConfig.getGemini().getApiKey();
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new BusinessException("未配置 Gemini API Key");
        }
        return apiKeys;
    }
}
