package com.wohoo_studio.backend.plumwhisperweb.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wohoo_studio.backend.plumwhisperweb.client.base.BaseApiClient;
import com.wohoo_studio.backend.plumwhisperweb.config.BaziAnalyzeConfig;
import com.wohoo_studio.backend.plumwhisperweb.dto.ai.model.GeminiRequestDTO;
import com.wohoo_studio.backend.plumwhisperweb.dto.ai.model.GeminiResponseDTO;
import com.wohoo_studio.backend.plumwhisperweb.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Gemini API 客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GeminiApiClient extends BaseApiClient {

    @Autowired
    private BaziAnalyzeConfig baziAnalyzeConfig;

    protected GeminiApiClient(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    public String chat(String prompt, String apiKey) {
        try {

            String model = baziAnalyzeConfig.getGemini().getModel();
            String url = String.format("%s/v1beta/models/%s:generateContent?key=%s",
                    baziAnalyzeConfig.getGemini().getBaseUrl(),
                    model,
                    apiKey);

            Request request = new Request.Builder()
                    .url(url)
                    .post(createJsonRequestBody(GeminiRequestDTO.createTextRequest(prompt)))
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 执行请求
            String responseJson = executeRequest(request);
            log.debug("Gemini API 响应: {}", responseJson);

            // 解析响应
            GeminiResponseDTO responseDTO = parseJsonResponse(responseJson, GeminiResponseDTO.class);

            // 提取响应文本
            String responseText = responseDTO.getFirstCandidateText();
            if (responseText == null || responseText.trim().isEmpty()) {
                throw new BusinessException("Gemini API 返回空响应");
            }

            return responseText;

        } catch (IOException e) {
            log.error("调用 Gemini API 失败", e);
            throw new BusinessException("调用 Gemini API 失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理 Gemini API 响应失败", e);
            throw new BusinessException("处理 Gemini API 响应失败: " + e.getMessage());
        }
    }
}
