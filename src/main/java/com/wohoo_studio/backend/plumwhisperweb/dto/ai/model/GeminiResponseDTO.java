package com.wohoo_studio.backend.plumwhisperweb.dto.ai.model;

import lombok.Data;

import java.util.List;

/**
 * Gemini API 响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class GeminiResponseDTO {

    /**
     * 候选响应列表
     */
    private List<Candidate> candidates;

    /**
     * 使用信息
     */
    private UsageMetadata usageMetadata;

    @Data
    public static class Candidate {
        /**
         * 内容
         */
        private Content content;

        /**
         * 完成原因
         */
        private String finishReason;

        /**
         * 索引
         */
        private Integer index;
    }

    @Data
    public static class Content {
        /**
         * 部分列表
         */
        private List<Part> parts;

        /**
         * 角色
         */
        private String role;
    }

    @Data
    public static class Part {
        /**
         * 文本内容
         */
        private String text;
    }

    @Data
    public static class UsageMetadata {
        /**
         * 提示令牌数
         */
        private Integer promptTokenCount;

        /**
         * 候选令牌数
         */
        private Integer candidatesTokenCount;

        /**
         * 总令牌数
         */
        private Integer totalTokenCount;
    }

    /**
     * 获取第一个候选响应的文本内容
     *
     * @return 响应文本
     */
    public String getFirstCandidateText() {
        if (candidates != null && !candidates.isEmpty()) {
            Candidate firstCandidate = candidates.get(0);
            if (firstCandidate.getContent() != null && 
                firstCandidate.getContent().getParts() != null && 
                !firstCandidate.getContent().getParts().isEmpty()) {
                return firstCandidate.getContent().getParts().get(0).getText();
            }
        }
        return null;
    }
}
