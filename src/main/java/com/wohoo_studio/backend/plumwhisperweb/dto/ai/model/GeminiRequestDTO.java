package com.wohoo_studio.backend.plumwhisperweb.dto.ai.model;

import lombok.Data;

import java.util.List;

/**
 * Gemini API 请求 DTO
 *
 * <AUTHOR>
 */
@Data
public class GeminiRequestDTO {

    /**
     * 内容列表
     */
    private List<Content> contents;

    @Data
    public static class Content {
        /**
         * 部分列表
         */
        private List<Part> parts;
    }

    @Data
    public static class Part {
        /**
         * 文本内容
         */
        private String text;
    }

    /**
     * 创建简单的文本请求
     *
     * @param prompt 提示文本
     * @return GeminiRequestDTO
     */
    public static GeminiRequestDTO createTextRequest(String prompt) {
        GeminiRequestDTO request = new GeminiRequestDTO();
        
        Part part = new Part();
        part.setText(prompt);
        
        Content content = new Content();
        content.setParts(List.of(part));
        
        request.setContents(List.of(content));
        
        return request;
    }
}
