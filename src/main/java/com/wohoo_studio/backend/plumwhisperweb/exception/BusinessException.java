package com.wohoo_studio.backend.plumwhisperweb.exception;

import com.wohoo_studio.backend.plumwhisperweb.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {

    private final Integer code;
    private final String message;

    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.FAILED.getCode();
        this.message = message;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
} 