package com.wohoo_studio.backend.plumwhisperweb.controller;

import com.wohoo_studio.backend.plumwhisperweb.client.GeminiApiClient;
import com.wohoo_studio.backend.plumwhisperweb.common.Result;
import com.wohoo_studio.backend.plumwhisperweb.vo.AiAnalyzeResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 八字分析控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/bazi")
public class BaziAnalyzeController {

    @Autowired
    private GeminiApiClient geminiApiClient;

    @PostMapping("/analyzeWithGemini")
    public Result<AiAnalyzeResVO> analyzeWithCustomKey(
            @RequestParam String userInput,
            @RequestParam String apiKey) {
        try {
            log.info("收到自定义API Key的八字分析请求: {}", userInput);

            // 使用自定义 API Key 调用 Gemini API
            String geminiResponse = geminiApiClient.chat(userInput, apiKey);

            // 构建响应
            AiAnalyzeResVO response = new AiAnalyzeResVO();
            response.setContent(geminiResponse);

            log.info("八字分析完成，响应长度: {}", geminiResponse.length());
            return Result.success(response);

        } catch (Exception e) {
            log.error("八字分析失败", e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }

}