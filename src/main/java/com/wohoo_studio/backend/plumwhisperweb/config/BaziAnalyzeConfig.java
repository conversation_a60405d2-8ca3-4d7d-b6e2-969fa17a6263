package com.wohoo_studio.backend.plumwhisperweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bazi.analyze")
public class BaziAnalyzeConfig {

    private GeminiConfig gemini = new GeminiConfig();

    @Data
    public static class GeminiConfig {
        private String baseUrl = "https://generativelanguage.googleapis.com";
        private List<String> apiKey;
        private String model = "gemini-1.5-flash";
    }

}
