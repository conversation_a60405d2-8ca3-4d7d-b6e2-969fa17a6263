package com.wohoo_studio.backend.plumwhisperweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bazi.analyze")
public class BaziAnalyzeConfig {

    private FeishuConfig feishu = new FeishuConfig();

    private GeminiConfig gemini = new GeminiConfig();

    private CacheConfig cache = new CacheConfig();

    @Data
    public static class FeishuConfig {
        private String baseUrl = "https://open.feishu.cn";
        private String appId;
        private String appSecret;
        private String tableId;
    }

    @Data
    public static class GeminiConfig {
        private String baseUrl = "https://generativelanguage.googleapis.com";
        private String apiKey;
        private String model = "gemini-1.5-flash";
    }

    @Data
    public static class CacheConfig {
        private Boolean enabled = true;
        private Integer feishuCacheMinutes = 30;
    }

}
