package com.wohoo_studio.backend.plumwhisperweb.client;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Gemini API 客户端测试
 *
 * <AUTHOR>
 */
@SpringBootTest
class GeminiApiClientTest {

    private static final Logger log = LoggerFactory.getLogger(GeminiApiClientTest.class);

    @Autowired
    private GeminiApiClient geminiApiClient;

    @Test
    void testChatWithCustomApiKey() {
        try {
            String prompt = "Generate a concise Bazi analysis in English based on the provided JSON input containing 五行分布, 四柱, 日主, 身强身弱, 喜用神颜色, and 推荐石头. The analysis should seamlessly include: 1) 'Your day master is [日主 in Chinese], and your destiny belongs to the [Strong/Weak] type.' 2) A brief explanation of the body strength (Strong/Weak), summarizing the 五行分布 in one sentence and incorporating the 四柱, 喜用神颜色, and 推荐石头 to reflect their balancing influence. Ensure the explanation is coherent, culturally relevant, and tailored to the input data.";
            String data = "{\"text\":\"{\\n  \\\"五行分布\\\": [\\n    {\\n      \\\"元素\\\": \\\"Earth\\\",\\n      \\\"比例\\\": \\\"26%\\\"\\n    },\\n    {\\n      \\\"元素\\\": \\\"Metal\\\",\\n      \\\"比例\\\": \\\"15%\\\"\\n    },\\n    {\\n      \\\"元素\\\": \\\"Water\\\",\\n      \\\"比例\\\": \\\"18%\\\"\\n    },\\n    {\\n      \\\"元素\\\": \\\"Wood\\\",\\n      \\\"比例\\\": \\\"25%\\\"\\n    },\\n    {\\n      \\\"元素\\\": \\\"Fire\\\",\\n      \\\"比例\\\": \\\"16%\\\"\\n    }\\n  ],\\n  \\\"日主\\\": \\\"水\\\",\\n  \\\"身强身弱\\\": \\\"身弱\\\",\\n  \\\"喜用神颜色\\\": [\\n    \\\"白色\\\",\\n    \\\"银色\\\",\\n    \\\"黑色\\\",\\n    \\\"蓝色\\\"\\n  ],\\n  \\\"用户关注点\\\": \\\"success\\\"\\n}\"}";
            String customApiKey = "AIzaSyAp4q7TV2efVWYJEiAHjClPsEnI2lljnqc";
            String response = geminiApiClient.chat(prompt + data, customApiKey);

            assertNotNull(response);
            assertFalse(response.trim().isEmpty());

            log.info("Gemini 响应: {}", response);
        } catch (Exception e) {
            log.warn("Gemini API 测试失败，可能是 API Key 无效: {}", e.getMessage());
        }
    }

}
